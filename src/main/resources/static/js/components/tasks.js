/**
 * Componente Tasks - Gestión de tareas
 */
window.TasksComponent = {
  name: 'tasks',
  currentTasks: [],
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener tareas
      this.currentTasks = await API.tasks.getAll();
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Tareas' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando tareas');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    return `
      <div class="tasks">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Tareas</h1>
              <p class="text-gray-600">Gestiona todas las tareas del sistema</p>
            </div>
            <div class="flex gap-2">
              <button class="btn btn-secondary" data-action="view-available">
                Tareas Disponibles
              </button>
              <button class="btn btn-primary" data-action="create">
                <span>➕</span>
                Nueva Tarea
              </button>
            </div>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-4">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="PENDING">Pendiente</option>
                  <option value="IN_PROGRESS">En Progreso</option>
                  <option value="COMPLETED">Completada</option>
                  <option value="CLAIMED">Reclamada</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Asignado a</label>
                <input type="text" class="form-input" id="assignee-filter" placeholder="Usuario asignado...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="order">Orden</option>
                  <option value="name">Nombre</option>
                  <option value="status">Estado</option>
                  <option value="assignedTo">Asignado a</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de tareas -->
        <div class="tasks-list" id="tasks-list">
          ${this.getTasksListTemplate()}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .gap-2 {
          gap: var(--spacing-2);
        }
        
        .task-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .task-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .task-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .task-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .task-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .task-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .task-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
        
        .task-order {
          background-color: var(--color-gray-100);
          color: var(--color-gray-700);
          padding: var(--spacing-1) var(--spacing-2);
          border-radius: var(--border-radius);
          font-size: var(--font-size-sm);
          font-weight: 500;
        }
      </style>
    `;
  },

  getTasksListTemplate() {
    if (this.currentTasks.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay tareas</h3>
            <p class="text-gray-600 mb-4">No se encontraron tareas en el sistema.</p>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Crear Primera Tarea
            </button>
          </div>
        </div>
      `;
    }

    return this.currentTasks.map(task => `
      <div class="task-card" data-id="${task.id}">
        <div class="task-header">
          <div>
            <h3 class="task-title">${DOMUtils.escapeHtml(task.name)}</h3>
            <div class="task-meta">
              <span class="task-order">Orden ${task.order}</span>
              ${task.formKey ? ` • Form: ${task.formKey}` : ''}
            </div>
          </div>
          <div class="task-actions">
            ${task.status === 'PENDING' && !task.assignedTo ? 
              `<button class="btn btn-sm btn-primary" data-action="claim" data-id="${task.id}">Reclamar</button>` : 
              ''
            }
            ${task.status === 'IN_PROGRESS' ? 
              `<button class="btn btn-sm btn-success" data-action="complete" data-id="${task.id}">Completar</button>` : 
              ''
            }
            <button class="btn btn-sm btn-outline" data-action="view-subtasks" data-id="${task.id}">
              Subtareas
            </button>
            <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${task.id}">
              Editar
            </button>
          </div>
        </div>
        
        ${task.description ? `<div class="task-description">${DOMUtils.escapeHtml(task.description)}</div>` : ''}
        
        <div class="task-footer">
          <div>
            ${Formatters.formatStatus(task.status)}
          </div>
          <div class="task-meta">
            ${task.assignedTo ? `Asignada a: ${Formatters.formatUsername(task.assignedTo)}` : 'Sin asignar'}
          </div>
        </div>
      </div>
    `).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar tareas</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener las tareas.</p>
          <button class="btn btn-primary" onclick="window.TasksComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  setupEventListeners() {
    // Búsqueda y filtros
    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const assigneeFilter = document.getElementById('assignee-filter');
    const sortSelect = document.getElementById('sort-select');
    
    if (searchInput) {
      searchInput.addEventListener('input', DOMUtils.debounce(() => this.filterTasks(), 300));
    }
    
    [statusFilter, assigneeFilter, sortSelect].forEach(element => {
      if (element) {
        element.addEventListener('change', () => this.filterTasks());
      }
    });

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterTasks() {
    console.log('Filtrar tareas');
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'view-available':
          await this.loadAvailableTasks();
          break;
        case 'claim':
          await this.claimTask(id);
          break;
        case 'complete':
          await this.completeTask(id);
          break;
        case 'view-subtasks':
          this.viewSubtasks(id);
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  showCreateModal() {
    console.log('Mostrar modal de creación de tarea');
  },

  async loadAvailableTasks() {
    try {
      DOMUtils.show('loading');
      this.currentTasks = await API.tasks.getAvailable();
      DOMUtils.hide('loading');
      
      const listContainer = document.getElementById('tasks-list');
      if (listContainer) {
        listContainer.innerHTML = this.getTasksListTemplate();
      }
    } catch (error) {
      DOMUtils.hide('loading');
      throw error;
    }
  },

  async claimTask(id) {
    const username = prompt('Ingresa tu nombre de usuario:');
    if (!username) return;
    
    await API.tasks.claim(id, username);
    await this.render(); // Recargar la vista
  },

  async completeTask(id) {
    if (confirm('¿Estás seguro de que quieres completar esta tarea?')) {
      await API.tasks.complete(id);
      await this.render(); // Recargar la vista
    }
  },

  viewSubtasks(id) {
    console.log('Ver subtareas de la tarea:', id);
  },

  showEditModal(id) {
    console.log('Mostrar modal de edición para tarea:', id);
  }
};

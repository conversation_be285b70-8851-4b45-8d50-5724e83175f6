/**
 * Componente Process Instances - Gestión de instancias de proceso
 */
window.ProcessInstancesComponent = {
  name: 'process-instances',
  currentInstances: [],
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener instancias de proceso
      this.currentInstances = await API.processInstances.getAll();
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Procesos' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando procesos');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    return `
      <div class="process-instances">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Procesos</h1>
              <p class="text-gray-600">Gestiona las instancias de proceso en ejecución</p>
            </div>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Nuevo Proceso
            </button>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-4">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="ACTIVE">Activo</option>
                  <option value="COMPLETED">Completado</option>
                  <option value="CANCELLED">Cancelado</option>
                  <option value="SUSPENDED">Suspendido</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Fecha de vencimiento</label>
                <select class="form-select" id="due-filter">
                  <option value="">Todas las fechas</option>
                  <option value="overdue">Vencidos</option>
                  <option value="today">Vencen hoy</option>
                  <option value="week">Vencen esta semana</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="createdAt">Fecha de creación</option>
                  <option value="name">Nombre</option>
                  <option value="dueDate">Fecha de vencimiento</option>
                  <option value="status">Estado</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de procesos -->
        <div class="instances-list" id="instances-list">
          ${this.getInstancesListTemplate()}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .instance-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .instance-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .instance-card.overdue {
          border-left: 4px solid var(--color-gray-800);
        }
        
        .instance-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .instance-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .instance-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .instance-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .instance-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .instance-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
        
        .instance-progress {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
        }
        
        .progress-bar {
          width: 100px;
          height: 8px;
          background-color: var(--color-gray-200);
          border-radius: 4px;
          overflow: hidden;
        }
        
        .progress-fill {
          height: 100%;
          background-color: var(--color-gray-600);
          transition: width 0.3s ease;
        }
      </style>
    `;
  },

  getInstancesListTemplate() {
    if (this.currentInstances.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay procesos</h3>
            <p class="text-gray-600 mb-4">Crea tu primer proceso para comenzar.</p>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Crear Primer Proceso
            </button>
          </div>
        </div>
      `;
    }

    return this.currentInstances.map(instance => {
      const isOverdue = instance.dueDate && new Date(instance.dueDate) < new Date();
      const progress = this.calculateProgress(instance);
      
      return `
        <div class="instance-card ${isOverdue ? 'overdue' : ''}" data-id="${instance.id}">
          <div class="instance-header">
            <div>
              <h3 class="instance-title">${DOMUtils.escapeHtml(instance.name)}</h3>
              <div class="instance-meta">
                Creado ${Formatters.formatRelativeTime(instance.createdAt)}
                ${instance.dueDate ? ` • Vence ${Formatters.formatDate(instance.dueDate)}` : ''}
              </div>
            </div>
            <div class="instance-actions">
              <button class="btn btn-sm btn-outline" data-action="view-tasks" data-id="${instance.id}">
                Tareas
              </button>
              <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${instance.id}">
                Editar
              </button>
              <button class="btn btn-sm btn-outline" data-action="view" data-id="${instance.id}">
                Ver Detalles
              </button>
            </div>
          </div>
          
          ${instance.description ? `<div class="instance-description">${DOMUtils.escapeHtml(instance.description)}</div>` : ''}
          
          <div class="instance-footer">
            <div class="instance-progress">
              <span class="instance-meta">Progreso:</span>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
              </div>
              <span class="instance-meta">${progress}%</span>
            </div>
            <div>
              ${Formatters.formatStatus(instance.status)}
              ${isOverdue ? '<span class="badge badge-danger ml-2">Vencido</span>' : ''}
            </div>
          </div>
        </div>
      `;
    }).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar procesos</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener los procesos.</p>
          <button class="btn btn-primary" onclick="window.ProcessInstancesComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  calculateProgress(instance) {
    // Simulación del cálculo de progreso
    // En una implementación real, esto se basaría en las tareas completadas
    switch (instance.status) {
      case 'COMPLETED':
        return 100;
      case 'ACTIVE':
        return Math.floor(Math.random() * 80) + 10; // 10-90%
      case 'SUSPENDED':
        return Math.floor(Math.random() * 50) + 10; // 10-60%
      default:
        return 0;
    }
  },

  setupEventListeners() {
    // Búsqueda
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.addEventListener('input', DOMUtils.debounce((e) => {
        this.filterInstances();
      }, 300));
    }

    // Filtros
    const statusFilter = document.getElementById('status-filter');
    const dueFilter = document.getElementById('due-filter');
    const sortSelect = document.getElementById('sort-select');
    
    [statusFilter, dueFilter, sortSelect].forEach(element => {
      if (element) {
        element.addEventListener('change', () => this.filterInstances());
      }
    });

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterInstances() {
    // Implementar filtrado similar al componente de plantillas
    console.log('Filtrar instancias');
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'view':
          this.viewInstance(id);
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        case 'view-tasks':
          this.viewInstanceTasks(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  async showCreateModal() {
    try {
      // Obtener plantillas activas para el selector
      const templates = await API.processDefinitions.getActive();

      const modalContent = this.getCreateModalTemplate(templates);
      const modalFooter = `
        <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
        <button type="button" class="btn btn-primary" id="save-instance-btn">Crear Proceso</button>
      `;

      ModalManager.show('Nuevo Proceso', modalContent, modalFooter);

      // Configurar event listeners del modal
      this.setupCreateModalListeners();

    } catch (error) {
      API.utils.handleError(error, 'cargando plantillas');
    }
  },

  viewInstance(id) {
    console.log('Ver detalles del proceso:', id);
  },

  showEditModal(id) {
    const instance = this.currentInstances.find(inst => inst.id === id);
    if (!instance) {
      window.NotificationManager.error('Proceso no encontrado');
      return;
    }

    const modalContent = this.getEditModalTemplate(instance);
    const modalFooter = `
      <button type="button" class="btn btn-secondary" onclick="ModalManager.hide()">Cancelar</button>
      <button type="button" class="btn btn-primary" id="update-instance-btn">Actualizar Proceso</button>
    `;

    ModalManager.show('Editar Proceso', modalContent, modalFooter);

    // Configurar event listeners del modal
    this.setupEditModalListeners(id);
  },

  viewInstanceTasks(id) {
    if (window.Router) {
      window.Router.navigate(`/tasks?process=${id}`);
    } else {
      window.location.hash = `#/tasks?process=${id}`;
    }
  },

  // ===== TEMPLATES DE MODALES =====

  getCreateModalTemplate(templates = []) {
    const templateOptions = templates.map(template =>
      `<option value="${template.id}">${DOMUtils.escapeHtml(template.name)} (v${template.version})</option>`
    ).join('');

    return `
      <form id="create-instance-form" class="instance-form">
        <div class="form-group">
          <label class="form-label" for="instance-template">Plantilla Base</label>
          <select class="form-select" id="instance-template" name="templateId">
            <option value="">Sin plantilla base</option>
            ${templateOptions}
          </select>
          <small class="form-help">Opcional. Selecciona una plantilla para heredar variables por defecto.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="instance-name">Nombre *</label>
          <input type="text" class="form-input" id="instance-name" name="name" required
                 placeholder="Nombre del proceso">
        </div>

        <div class="form-group">
          <label class="form-label" for="instance-description">Descripción</label>
          <textarea class="form-textarea" id="instance-description" name="description"
                    placeholder="Descripción del proceso" rows="3"></textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="instance-due-date">Fecha de Vencimiento *</label>
          <input type="date" class="form-input" id="instance-due-date" name="dueDate" required>
          <small class="form-help">Fecha límite para completar el proceso.</small>
        </div>

        <div class="form-group">
          <label class="form-label" for="instance-variables">Variables del Proceso (JSON)</label>
          <textarea class="form-textarea" id="instance-variables" name="variables"
                    placeholder='{"variable1": "valor1", "variable2": "valor2"}' rows="4"></textarea>
          <small class="form-help">Formato JSON válido. Se combinarán con las variables de la plantilla si se selecciona una.</small>
        </div>
      </form>
    `;
  },

  getEditModalTemplate(instance) {
    const variables = instance.variables ?
      (typeof instance.variables === 'string' ?
        instance.variables :
        JSON.stringify(instance.variables, null, 2)) : '';

    const dueDateValue = instance.dueDate ? instance.dueDate.split('T')[0] : '';

    return `
      <form id="edit-instance-form" class="instance-form">
        <div class="form-group">
          <label class="form-label" for="edit-instance-name">Nombre *</label>
          <input type="text" class="form-input" id="edit-instance-name" name="name" required
                 value="${DOMUtils.escapeHtml(instance.name)}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-instance-description">Descripción</label>
          <textarea class="form-textarea" id="edit-instance-description" name="description"
                    rows="3">${DOMUtils.escapeHtml(instance.description || '')}</textarea>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-instance-due-date">Fecha de Vencimiento *</label>
          <input type="date" class="form-input" id="edit-instance-due-date" name="dueDate" required
                 value="${dueDateValue}">
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-instance-status">Estado</label>
          <select class="form-select" id="edit-instance-status" name="status">
            <option value="ACTIVE" ${instance.status === 'ACTIVE' ? 'selected' : ''}>Activo</option>
            <option value="COMPLETED" ${instance.status === 'COMPLETED' ? 'selected' : ''}>Completado</option>
            <option value="CANCELLED" ${instance.status === 'CANCELLED' ? 'selected' : ''}>Cancelado</option>
            <option value="SUSPENDED" ${instance.status === 'SUSPENDED' ? 'selected' : ''}>Suspendido</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label" for="edit-instance-variables">Variables del Proceso (JSON)</label>
          <textarea class="form-textarea" id="edit-instance-variables" name="variables"
                    rows="4">${DOMUtils.escapeHtml(variables)}</textarea>
          <small class="form-help">Formato JSON válido.</small>
        </div>
      </form>
    `;
  },

  // ===== EVENT LISTENERS DE MODALES =====

  setupCreateModalListeners() {
    const saveBtn = document.getElementById('save-instance-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', async () => {
        await this.handleCreateSubmit();
      });
    }

    // Listener para cambio de plantilla
    const templateSelect = document.getElementById('instance-template');
    if (templateSelect) {
      templateSelect.addEventListener('change', (e) => {
        this.handleTemplateChange(e.target.value);
      });
    }
  },

  setupEditModalListeners(id) {
    const updateBtn = document.getElementById('update-instance-btn');
    if (updateBtn) {
      updateBtn.addEventListener('click', async () => {
        await this.handleEditSubmit(id);
      });
    }
  },

  async handleTemplateChange(templateId) {
    if (!templateId) return;

    try {
      const template = await API.processDefinitions.getById(templateId);
      const variablesTextarea = document.getElementById('instance-variables');

      if (template.defaultVariables && variablesTextarea) {
        const defaultVars = typeof template.defaultVariables === 'string' ?
          template.defaultVariables :
          JSON.stringify(template.defaultVariables, null, 2);
        variablesTextarea.value = defaultVars;
      }
    } catch (error) {
      console.warn('Error cargando variables de plantilla:', error);
    }
  },

  async handleCreateSubmit() {
    const form = document.getElementById('create-instance-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        templateId: formData.get('templateId') || null,
        name: formData.get('name'),
        description: formData.get('description') || null,
        dueDate: formData.get('dueDate'),
        variables: this.parseJsonField(formData.get('variables'))
      };

      await API.processInstances.create(data);

      ModalManager.hide();
      window.NotificationManager.success('Proceso creado exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'creando proceso');
    }
  },

  async handleEditSubmit(id) {
    const form = document.getElementById('edit-instance-form');
    if (!form || !DOMUtils.validateForm(form)) {
      return;
    }

    try {
      const formData = new FormData(form);
      const data = {
        name: formData.get('name'),
        description: formData.get('description') || null,
        dueDate: formData.get('dueDate'),
        status: formData.get('status'),
        variables: this.parseJsonField(formData.get('variables'))
      };

      await API.processInstances.update(id, data);

      ModalManager.hide();
      window.NotificationManager.success('Proceso actualizado exitosamente');
      await this.render(); // Recargar la lista

    } catch (error) {
      API.utils.handleError(error, 'actualizando proceso');
    }
  },

  parseJsonField(jsonString) {
    if (!jsonString || !jsonString.trim()) {
      return null;
    }

    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error('El formato JSON no es válido');
    }
  }
};

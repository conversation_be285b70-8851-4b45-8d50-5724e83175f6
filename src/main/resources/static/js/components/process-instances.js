/**
 * Componente Process Instances - Gestión de instancias de proceso
 */
window.ProcessInstancesComponent = {
  name: 'process-instances',
  currentInstances: [],
  
  async render() {
    try {
      DOMUtils.show('loading');
      
      // Obtener instancias de proceso
      this.currentInstances = await API.processInstances.getAll();
      
      DOMUtils.hide('loading');
      
      // Actualizar breadcrumb
      DOMUtils.updateBreadcrumb([
        { text: 'Inicio', href: '#/' },
        { text: 'Procesos' }
      ]);

      // Renderizar contenido
      const content = document.getElementById('app-content');
      content.innerHTML = this.getTemplate();

      // Configurar event listeners
      this.setupEventListeners();

    } catch (error) {
      DOMUtils.hide('loading');
      API.utils.handleError(error, 'cargando procesos');
      
      const content = document.getElementById('app-content');
      content.innerHTML = this.getErrorTemplate();
    }
  },

  getTemplate() {
    return `
      <div class="process-instances">
        <div class="page-header mb-6">
          <div class="flex justify-between items-center">
            <div>
              <h1>Procesos</h1>
              <p class="text-gray-600">Gestiona las instancias de proceso en ejecución</p>
            </div>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Nuevo Proceso
            </button>
          </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="grid grid-cols-4">
              <div class="form-group">
                <label class="form-label">Buscar</label>
                <input type="text" class="form-input" id="search-input" placeholder="Buscar por nombre...">
              </div>
              
              <div class="form-group">
                <label class="form-label">Estado</label>
                <select class="form-select" id="status-filter">
                  <option value="">Todos los estados</option>
                  <option value="ACTIVE">Activo</option>
                  <option value="COMPLETED">Completado</option>
                  <option value="CANCELLED">Cancelado</option>
                  <option value="SUSPENDED">Suspendido</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Fecha de vencimiento</label>
                <select class="form-select" id="due-filter">
                  <option value="">Todas las fechas</option>
                  <option value="overdue">Vencidos</option>
                  <option value="today">Vencen hoy</option>
                  <option value="week">Vencen esta semana</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">Ordenar por</label>
                <select class="form-select" id="sort-select">
                  <option value="createdAt">Fecha de creación</option>
                  <option value="name">Nombre</option>
                  <option value="dueDate">Fecha de vencimiento</option>
                  <option value="status">Estado</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de procesos -->
        <div class="instances-list" id="instances-list">
          ${this.getInstancesListTemplate()}
        </div>
      </div>

      <style>
        .page-header h1 {
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-primary);
          margin-bottom: 0.5rem;
        }
        
        .flex {
          display: flex;
        }
        
        .justify-between {
          justify-content: space-between;
        }
        
        .items-center {
          align-items: center;
        }
        
        .instance-card {
          background: var(--color-white);
          border: 1px solid var(--color-gray-200);
          border-radius: var(--border-radius-lg);
          padding: var(--spacing-4);
          margin-bottom: var(--spacing-4);
          transition: var(--transition);
        }
        
        .instance-card:hover {
          box-shadow: var(--shadow-md);
        }
        
        .instance-card.overdue {
          border-left: 4px solid var(--color-gray-800);
        }
        
        .instance-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-3);
        }
        
        .instance-title {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--color-gray-800);
          margin-bottom: var(--spacing-1);
        }
        
        .instance-meta {
          font-size: var(--font-size-sm);
          color: var(--color-gray-500);
        }
        
        .instance-actions {
          display: flex;
          gap: var(--spacing-2);
        }
        
        .instance-description {
          color: var(--color-gray-600);
          margin-bottom: var(--spacing-3);
        }
        
        .instance-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: var(--spacing-3);
          border-top: 1px solid var(--color-gray-200);
        }
        
        .instance-progress {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
        }
        
        .progress-bar {
          width: 100px;
          height: 8px;
          background-color: var(--color-gray-200);
          border-radius: 4px;
          overflow: hidden;
        }
        
        .progress-fill {
          height: 100%;
          background-color: var(--color-gray-600);
          transition: width 0.3s ease;
        }
      </style>
    `;
  },

  getInstancesListTemplate() {
    if (this.currentInstances.length === 0) {
      return `
        <div class="card">
          <div class="card-body text-center">
            <h3>No hay procesos</h3>
            <p class="text-gray-600 mb-4">Crea tu primer proceso para comenzar.</p>
            <button class="btn btn-primary" data-action="create">
              <span>➕</span>
              Crear Primer Proceso
            </button>
          </div>
        </div>
      `;
    }

    return this.currentInstances.map(instance => {
      const isOverdue = instance.dueDate && new Date(instance.dueDate) < new Date();
      const progress = this.calculateProgress(instance);
      
      return `
        <div class="instance-card ${isOverdue ? 'overdue' : ''}" data-id="${instance.id}">
          <div class="instance-header">
            <div>
              <h3 class="instance-title">${DOMUtils.escapeHtml(instance.name)}</h3>
              <div class="instance-meta">
                Creado ${Formatters.formatRelativeTime(instance.createdAt)}
                ${instance.dueDate ? ` • Vence ${Formatters.formatDate(instance.dueDate)}` : ''}
              </div>
            </div>
            <div class="instance-actions">
              <button class="btn btn-sm btn-outline" data-action="view-tasks" data-id="${instance.id}">
                Tareas
              </button>
              <button class="btn btn-sm btn-secondary" data-action="edit" data-id="${instance.id}">
                Editar
              </button>
              <button class="btn btn-sm btn-outline" data-action="view" data-id="${instance.id}">
                Ver Detalles
              </button>
            </div>
          </div>
          
          ${instance.description ? `<div class="instance-description">${DOMUtils.escapeHtml(instance.description)}</div>` : ''}
          
          <div class="instance-footer">
            <div class="instance-progress">
              <span class="instance-meta">Progreso:</span>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
              </div>
              <span class="instance-meta">${progress}%</span>
            </div>
            <div>
              ${Formatters.formatStatus(instance.status)}
              ${isOverdue ? '<span class="badge badge-danger ml-2">Vencido</span>' : ''}
            </div>
          </div>
        </div>
      `;
    }).join('');
  },

  getErrorTemplate() {
    return `
      <div class="card">
        <div class="card-body text-center">
          <h2>Error al cargar procesos</h2>
          <p class="text-gray-600 mb-4">No se pudieron obtener los procesos.</p>
          <button class="btn btn-primary" onclick="window.ProcessInstancesComponent.render()">
            Reintentar
          </button>
        </div>
      </div>
    `;
  },

  calculateProgress(instance) {
    // Simulación del cálculo de progreso
    // En una implementación real, esto se basaría en las tareas completadas
    switch (instance.status) {
      case 'COMPLETED':
        return 100;
      case 'ACTIVE':
        return Math.floor(Math.random() * 80) + 10; // 10-90%
      case 'SUSPENDED':
        return Math.floor(Math.random() * 50) + 10; // 10-60%
      default:
        return 0;
    }
  },

  setupEventListeners() {
    // Búsqueda
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
      searchInput.addEventListener('input', DOMUtils.debounce((e) => {
        this.filterInstances();
      }, 300));
    }

    // Filtros
    const statusFilter = document.getElementById('status-filter');
    const dueFilter = document.getElementById('due-filter');
    const sortSelect = document.getElementById('sort-select');
    
    [statusFilter, dueFilter, sortSelect].forEach(element => {
      if (element) {
        element.addEventListener('change', () => this.filterInstances());
      }
    });

    // Acciones de botones
    document.addEventListener('click', (e) => {
      const actionButton = e.target.closest('[data-action]');
      if (!actionButton) return;

      const action = actionButton.dataset.action;
      const id = actionButton.dataset.id;

      this.handleAction(action, id);
    });
  },

  filterInstances() {
    // Implementar filtrado similar al componente de plantillas
    console.log('Filtrar instancias');
  },

  async handleAction(action, id) {
    try {
      switch (action) {
        case 'create':
          this.showCreateModal();
          break;
        case 'view':
          this.viewInstance(id);
          break;
        case 'edit':
          this.showEditModal(id);
          break;
        case 'view-tasks':
          this.viewInstanceTasks(id);
          break;
        default:
          console.warn('Acción no reconocida:', action);
      }
    } catch (error) {
      API.utils.handleError(error, `ejecutando acción ${action}`);
    }
  },

  showCreateModal() {
    console.log('Mostrar modal de creación de proceso');
  },

  viewInstance(id) {
    console.log('Ver detalles del proceso:', id);
  },

  showEditModal(id) {
    console.log('Mostrar modal de edición para proceso:', id);
  },

  viewInstanceTasks(id) {
    if (window.Router) {
      window.Router.navigate(`/tasks?process=${id}`);
    } else {
      window.location.hash = `#/tasks?process=${id}`;
    }
  }
};

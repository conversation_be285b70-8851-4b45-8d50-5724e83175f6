<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Modales - Proezedure</title>
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/components.css">
</head>
<body>
    <div class="container" style="padding: 2rem;">
        <h1>Prueba de Modales</h1>
        <p>Esta página permite probar la funcionalidad de modales sin necesidad de datos en la base.</p>
        
        <div class="grid grid-cols-3" style="gap: 2rem; margin-top: 2rem;">
            <!-- Plantillas de Proceso -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Plantillas de Proceso</h3>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary mb-4" onclick="testCreateDefinitionModal()">
                        ➕ Nueva Plantilla
                    </button>
                    <button class="btn btn-secondary" onclick="testEditDefinitionModal()">
                        ✏️ Editar Plantilla
                    </button>
                </div>
            </div>
            
            <!-- Instancias de Proceso -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Instancias de Proceso</h3>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary mb-4" onclick="testCreateInstanceModal()">
                        ➕ Nuevo Proceso
                    </button>
                    <button class="btn btn-secondary" onclick="testEditInstanceModal()">
                        ✏️ Editar Proceso
                    </button>
                </div>
            </div>
            
            <!-- Tareas -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Tareas</h3>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary mb-4" onclick="testCreateTaskModal()">
                        ➕ Nueva Tarea
                    </button>
                    <button class="btn btn-secondary" onclick="testEditTaskModal()">
                        ✏️ Editar Tarea
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-6">
            <div class="card-header">
                <h3 class="card-title">Instrucciones de Prueba</h3>
            </div>
            <div class="card-body">
                <ol>
                    <li>Haz clic en cualquier botón para abrir el modal correspondiente</li>
                    <li>Verifica que el modal se abra correctamente</li>
                    <li>Prueba llenar los formularios con datos válidos e inválidos</li>
                    <li>Verifica la validación de campos requeridos y formato JSON</li>
                    <li>Prueba cerrar el modal con el botón X, overlay o tecla Escape</li>
                    <li>Verifica que las notificaciones aparezcan correctamente</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Modal para confirmaciones y formularios -->
    <div class="modal" id="modal" role="dialog" aria-hidden="true">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-title">Título</h2>
                <button class="modal-close" id="modal-close" aria-label="Cerrar modal">×</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Contenido del modal -->
            </div>
            <div class="modal-footer" id="modal-footer">
                <!-- Botones del modal -->
            </div>
        </div>
    </div>

    <!-- Notificaciones toast -->
    <div class="toast-container" id="toast-container" aria-live="polite" aria-atomic="true">
        <!-- Las notificaciones se insertan aquí dinámicamente -->
    </div>

    <!-- Scripts JavaScript -->
    <script src="/js/utils/dom.js"></script>
    <script src="/js/utils/formatters.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/components/process-definitions.js"></script>
    <script src="/js/components/process-instances.js"></script>
    <script src="/js/components/tasks.js"></script>
    <script src="/js/app.js"></script>
    
    <script>
        // Datos de prueba
        const mockDefinition = {
            id: 'test-def-123',
            name: 'Proceso de Aprobación',
            description: 'Proceso para aprobar solicitudes',
            version: '1.0.0',
            status: 'ACTIVE',
            defaultVariables: '{"cliente": "Juan Pérez", "monto": 50000}'
        };
        
        const mockInstance = {
            id: 'test-inst-456',
            name: 'Solicitud #001',
            description: 'Solicitud de crédito',
            status: 'ACTIVE',
            dueDate: '2025-12-31',
            variables: '{"cliente": "María García", "monto": 75000}'
        };
        
        const mockTask = {
            id: 'test-task-789',
            name: 'Revisar Documentos',
            description: 'Revisar documentación del cliente',
            formKey: 'review_documents',
            order: 1,
            status: 'PENDING',
            assignedTo: '<EMAIL>',
            inputs: '{"documentos": ["cedula", "ingresos"]}',
            outputs: '{"aprobado": true, "observaciones": "Todo correcto"}'
        };
        
        // Funciones de prueba
        function testCreateDefinitionModal() {
            window.ProcessDefinitionsComponent.showCreateModal();
        }
        
        function testEditDefinitionModal() {
            // Simular que existe la definición en la lista
            window.ProcessDefinitionsComponent.currentDefinitions = [mockDefinition];
            window.ProcessDefinitionsComponent.showEditModal(mockDefinition.id);
        }
        
        async function testCreateInstanceModal() {
            // Mock de plantillas para el selector
            window.API.processDefinitions.getActive = async () => [mockDefinition];
            await window.ProcessInstancesComponent.showCreateModal();
        }
        
        function testEditInstanceModal() {
            // Simular que existe la instancia en la lista
            window.ProcessInstancesComponent.currentInstances = [mockInstance];
            window.ProcessInstancesComponent.showEditModal(mockInstance.id);
        }
        
        async function testCreateTaskModal() {
            // Mock de procesos para el selector
            window.API.processInstances.getAll = async () => [mockInstance];
            await window.TasksComponent.showCreateModal();
        }
        
        function testEditTaskModal() {
            // Simular que existe la tarea en la lista
            window.TasksComponent.currentTasks = [mockTask];
            window.TasksComponent.showEditModal(mockTask.id);
        }
        
        // Inicializar sistema de notificaciones
        document.addEventListener('DOMContentLoaded', () => {
            window.App.initNotifications();
            window.App.setupModal();
            
            // Mostrar mensaje de bienvenida
            setTimeout(() => {
                window.NotificationManager.info('Sistema de modales listo para pruebas');
            }, 500);
        });
    </script>
</body>
</html>
